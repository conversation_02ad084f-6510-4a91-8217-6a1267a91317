stages:
  - build

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker build -t cr.ixdev.cn/cnix/cbdv/docker-bridge:$CI_COMMIT_SHA .
    - docker tag cr.ixdev.cn/cnix/cbdv/docker-bridge:$CI_COMMIT_SHA cr.ixdev.cn/cnix/cbdv/docker-bridge:latest
    - docker push cr.ixdev.cn/cnix/cbdv/docker-bridge
