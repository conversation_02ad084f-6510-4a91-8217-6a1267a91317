import os
import shutil
import tempfile
import logging
from pathlib import Path
from typing import List, Optional

from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel

from .config import settings
from .docker_service import docker_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Docker Service API",
    description="简单的Docker镜像构建和推送服务",
    version="1.0.0"
)


class PullPushRequest(BaseModel):
    source_image: str
    target_image: str
    tag: str = "latest"


class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None


@app.get("/")
async def root():
    return {"message": "Docker Service API", "version": "1.0.0"}


@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    return ApiResponse(
        success=True,
        message="Service is healthy",
        data={"status": "ok"}
    )


@app.post("/api/pull-push")
async def pull_and_push(request: PullPushRequest):
    """拉取镜像并推送到library registry"""
    try:
        logger.info(f"开始拉取并推送镜像: {request.source_image} -> {request.target_image}:{request.tag}")
        
        success, message = docker_service.pull_and_push_image(
            source_image=request.source_image,
            target_image=request.target_image,
            tag=request.tag
        )
        
        if success:
            return ApiResponse(
                success=True,
                message=message,
                data={
                    "source_image": request.source_image,
                    "target_image": request.target_image,
                    "tag": request.tag
                }
            )
        else:
            raise HTTPException(status_code=500, detail=message)
            
    except Exception as e:
        logger.error(f"拉取推送镜像失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/build-from-files")
async def build_from_files(
    files: List[UploadFile] = File(...),
    image_name: str = Form(...),
    tag: str = Form(default="latest"),
    base_image: Optional[str] = Form(default=None)
):
    """从上传的文件构建镜像并推送到artifacts registry"""
    temp_dir = None
    try:
        logger.info(f"开始从文件构建镜像: {image_name}:{tag}")
        
        # 创建临时目录
        temp_dir = Path(tempfile.mkdtemp(dir=settings.temp_dir))
        
        # 保存上传的文件
        saved_files = []
        for file in files:
            if file.size and file.size > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件 {file.filename} 超过最大大小限制 ({settings.max_file_size} bytes)"
                )
            
            file_path = temp_dir / file.filename
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            saved_files.append(file.filename)
        
        logger.info(f"保存了 {len(saved_files)} 个文件: {saved_files}")
        
        # 构建并推送镜像
        success, message = docker_service.build_and_push_from_files(
            files_dir=temp_dir,
            image_name=image_name,
            tag=tag,
            base_image=base_image
        )
        
        if success:
            return ApiResponse(
                success=True,
                message=message,
                data={
                    "image_name": image_name,
                    "tag": tag,
                    "base_image": base_image or settings.default_base_image,
                    "files": saved_files
                }
            )
        else:
            raise HTTPException(status_code=500, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从文件构建镜像失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 清理临时目录
        if temp_dir:
            docker_service.cleanup_temp_dir(temp_dir)


@app.get("/api/config")
async def get_config():
    """获取当前配置信息（不包含敏感信息）"""
    return ApiResponse(
        success=True,
        message="Configuration retrieved",
        data={
            "registry_library_url": settings.registry_library_url,
            "registry_artifacts_url": settings.registry_artifacts_url,
            "default_base_image": settings.default_base_image,
            "max_file_size": settings.max_file_size,
            "temp_dir": settings.temp_dir
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=True
    )
