import subprocess
import os
import shutil
import tempfile
import logging
from typing import Optional, List
from pathlib import Path

from .config import settings, get_full_image_name

logger = logging.getLogger(__name__)


class DockerService:
    def __init__(self):
        self.temp_dir = Path(settings.temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
    
    def _run_command(self, cmd: List[str]) -> tuple[bool, str]:
        """执行shell命令并返回结果"""
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            error_msg = f"命令执行失败: {e.stderr}"
            logger.error(error_msg)
            return False, error_msg
    
    def login_registry(self) -> bool:
        """登录到registry"""
        if not settings.registry_username or not settings.registry_password:
            logger.warning("未配置registry认证信息")
            return True
        
        # 登录library registry
        cmd = [
            "docker", "login",
            settings.registry_library_url,
            "-u", settings.registry_username,
            "-p", settings.registry_password
        ]
        success, _ = self._run_command(cmd)
        if not success:
            return False
        
        # 登录artifacts registry
        cmd = [
            "docker", "login", 
            settings.registry_artifacts_url,
            "-u", settings.registry_username,
            "-p", settings.registry_password
        ]
        return self._run_command(cmd)[0]
    
    def pull_and_push_image(self, source_image: str, target_image: str, tag: str = "latest") -> tuple[bool, str]:
        """拉取镜像并推送到library registry"""
        try:
            # 登录registry
            if not self.login_registry():
                return False, "Registry登录失败"
            
            # 拉取源镜像
            pull_cmd = ["docker", "pull", source_image]
            success, output = self._run_command(pull_cmd)
            if not success:
                return False, f"拉取镜像失败: {output}"
            
            # 构建目标镜像名称
            full_target_name = get_full_image_name(target_image, tag, is_artifact=False)
            
            # 重新标记镜像
            tag_cmd = ["docker", "tag", source_image, full_target_name]
            success, output = self._run_command(tag_cmd)
            if not success:
                return False, f"标记镜像失败: {output}"
            
            # 推送镜像
            push_cmd = ["docker", "push", full_target_name]
            success, output = self._run_command(push_cmd)
            if not success:
                return False, f"推送镜像失败: {output}"
            
            # 清理本地镜像
            self._cleanup_image(source_image)
            self._cleanup_image(full_target_name)
            
            return True, f"成功推送镜像: {full_target_name}"
            
        except Exception as e:
            return False, f"操作失败: {str(e)}"
    
    def build_and_push_from_files(self, files_dir: Path, image_name: str, tag: str = "latest", 
                                 base_image: str = None) -> tuple[bool, str]:
        """从文件构建镜像并推送到artifacts registry"""
        try:
            # 登录registry
            if not self.login_registry():
                return False, "Registry登录失败"
            
            # 使用默认基础镜像
            if not base_image:
                base_image = settings.default_base_image
            
            # 创建Dockerfile
            dockerfile_path = files_dir / "Dockerfile"
            dockerfile_content = f"""FROM {base_image}
COPY . /app
WORKDIR /app
CMD ["sh"]
"""
            dockerfile_path.write_text(dockerfile_content)
            
            # 构建目标镜像名称
            full_image_name = get_full_image_name(image_name, tag, is_artifact=True)
            
            # 构建镜像
            build_cmd = ["docker", "build", "-t", full_image_name, str(files_dir)]
            success, output = self._run_command(build_cmd)
            if not success:
                return False, f"构建镜像失败: {output}"
            
            # 推送镜像
            push_cmd = ["docker", "push", full_image_name]
            success, output = self._run_command(push_cmd)
            if not success:
                return False, f"推送镜像失败: {output}"
            
            # 清理本地镜像
            self._cleanup_image(full_image_name)
            
            return True, f"成功构建并推送镜像: {full_image_name}"
            
        except Exception as e:
            return False, f"操作失败: {str(e)}"
    
    def _cleanup_image(self, image_name: str):
        """清理本地镜像"""
        try:
            cmd = ["docker", "rmi", image_name, "-f"]
            self._run_command(cmd)
        except Exception:
            pass  # 忽略清理失败
    
    def cleanup_temp_dir(self, temp_path: Path):
        """清理临时目录"""
        try:
            if temp_path.exists():
                shutil.rmtree(temp_path)
        except Exception as e:
            logger.warning(f"清理临时目录失败: {e}")


# 全局Docker服务实例
docker_service = DockerService()
