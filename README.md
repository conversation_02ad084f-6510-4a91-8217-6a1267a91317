# Docker Service API

一个简单的Python服务，用于Docker镜像的拉取、构建和推送操作。

## 功能特性

- **Pull & Push**: 从源registry拉取镜像并推送到 `registry.datasecchk.net/library`
- **Build from Files**: 从上传的文件构建Docker镜像并推送到 `registry.datasecchk.net/artifacts`
- **简单易用**: 基于FastAPI的RESTful API接口

## 快速开始

### 1. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的registry认证信息
```

### 2. 使用Docker Compose启动

```bash
docker-compose up -d
```

服务将在 `http://localhost:8000` 启动。

### 3. 查看API文档

访问 `http://localhost:8000/docs` 查看自动生成的API文档。

## API接口

### 健康检查
```
GET /api/health
```

### 拉取并推送镜像
```
POST /api/pull-push
Content-Type: application/json

{
    "source_image": "nginx:latest",
    "target_image": "my-nginx",
    "tag": "v1.0"
}
```

### 从文件构建镜像
```
POST /api/build-from-files
Content-Type: multipart/form-data

files: [文件1, 文件2, ...]
image_name: my-app
tag: v1.0
base_image: alpine:latest (可选)
```

### 获取配置信息
```
GET /api/config
```

## 项目结构

```
docker-service/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用
│   ├── docker_service.py    # Docker操作服务
│   └── config.py            # 配置管理
├── templates/
│   └── Dockerfile.template  # Dockerfile模板
├── temp/                    # 临时文件目录
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| REGISTRY_LIBRARY_URL | Library镜像registry地址 | registry.datasecchk.net/library |
| REGISTRY_ARTIFACTS_URL | Artifacts镜像registry地址 | registry.datasecchk.net/artifacts |
| REGISTRY_USERNAME | Registry用户名 | - |
| REGISTRY_PASSWORD | Registry密码 | - |
| TEMP_DIR | 临时文件目录 | ./temp |
| DEFAULT_BASE_IMAGE | 默认基础镜像 | alpine:latest |
| MAX_FILE_SIZE | 最大文件大小(字节) | 104857600 |

## 注意事项

1. 服务需要访问Docker daemon，因此需要挂载 `/var/run/docker.sock`
2. 上传的文件会临时保存在 `temp` 目录，构建完成后自动清理
3. 构建的镜像会在推送后自动从本地删除以节省空间
4. 确保registry认证信息正确配置

## 开发模式

```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```
