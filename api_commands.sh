#!/bin/bash

# Docker API 常用命令集合
# 端口: 8015

API_BASE="http://localhost:8015"

# 健康检查
alias api-health="curl -s ${API_BASE}/api/health | python3 -m json.tool"

# 获取配置
alias api-config="curl -s ${API_BASE}/api/config | python3 -m json.tool"

# Pull-Push 函数
pull-push() {
    if [ $# -lt 2 ]; then
        echo "用法: pull-push <源镜像> <目标镜像> [标签]"
        echo "示例: pull-push nginx:latest my-registry/nginx latest"
        return 1
    fi
    
    local source_image="$1"
    local target_image="$2"
    local tag="${3:-latest}"
    
    curl -X POST ${API_BASE}/api/pull-push \
        -H "Content-Type: application/json" \
        -d "{
            \"source_image\": \"${source_image}\",
            \"target_image\": \"${target_image}\",
            \"tag\": \"${tag}\"
        }" | python3 -m json.tool
}

# 文件上传构建函数
build-upload() {
    if [ $# -lt 2 ]; then
        echo "用法: build-upload <文件路径> <镜像名> [标签] [基础镜像]"
        echo "示例: build-upload app.py my-app latest python:3.9"
        return 1
    fi
    
    local file_path="$1"
    local image_name="$2"
    local tag="${3:-latest}"
    local base_image="$4"
    
    if [ ! -f "$file_path" ]; then
        echo "错误: 文件不存在: $file_path"
        return 1
    fi
    
    local cmd="curl -X POST ${API_BASE}/api/build-from-files -F \"files=@${file_path}\" -F \"image_name=${image_name}\" -F \"tag=${tag}\""
    
    if [ -n "$base_image" ]; then
        cmd="${cmd} -F \"base_image=${base_image}\""
    fi
    
    eval "$cmd | python3 -m json.tool"
}

# 显示所有可用命令
show-commands() {
    echo "可用的API命令:"
    echo "  api-health              - 检查API健康状态"
    echo "  api-config              - 获取API配置"
    echo "  pull-push <源> <目标> [标签] - 拉取并推送镜像"
    echo "  build-upload <文件> <镜像名> [标签] [基础镜像] - 上传文件构建镜像"
    echo "  show-commands           - 显示此帮助"
    echo ""
    echo "示例:"
    echo "  pull-push nginx:latest my-registry/nginx"
    echo "  build-upload app.py my-python-app v1.0 python:3.9"
}

echo "Docker API 命令已加载 (端口: 8015)"
echo "输入 'show-commands' 查看所有可用命令"
